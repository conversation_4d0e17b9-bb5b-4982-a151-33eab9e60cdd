# LobeChat 世界书激活实现方案 v4

## 🎯 宏观设计视角

### 核心目标
在LobeChat现有架构中集成世界书激活功能，通过后端统一处理实现消息预处理时的智能内容注入。

### 设计原则
1. **后端统一处理**：将世界书激活逻辑从前端移至后端，实现统一管理
2. **模块化架构**：按功能职责拆分文件，每个模块职责单一
3. **引擎化设计**：采用统一引擎架构，支持关键词匹配、过滤、概率控制等功能
4. **最小化前端修改**：仅修改消息处理调用方式，保持现有逻辑不变
5. **渐进式集成**：与现有预设系统无缝集成，支持错误回退

### 整体架构
```
前端消息处理 → 后端增强API → 世界书激活服务 → 预设处理服务 → 增强消息返回
```

## 📁 文件清单

### 新增文件

#### 后端API接口
- **文件**：`src/app/(backend)/webapi/chat/enhanced-messages/route.ts`
- **功能**：接收messages和topicId，调用世界书激活和预设处理，返回增强消息
- **核心方法**：POST处理器，身份验证，错误处理

#### 世界书激活服务（新增）
- **文件**：`src/server/services/worldbook/activation-service.ts`
- **功能**：主控制器，协调各个激活模块和处理器
- **核心方法**：
  - `activate()` - 主激活方法，协调整个激活流程
  - `buildActivationContext()` - 构建激活上下文
  - `finalizeActivation()` - 最终激活处理和结果整合

#### 激活模式处理器（新增）
- **目录**：`src/server/services/worldbook/activators/`
- **文件列表**：
  - `constant-activator.ts` - Constant模式处理器
  - `keyword-activator.ts` - Keyword模式处理器
  - `vector-activator.ts` - Vector模式处理器
  - `sticky-activator.ts` - Sticky模式处理器
  - `decorator-activator.ts` - Decorator模式处理器
  - `index.ts` - 激活器统一导出

#### 专门处理器（新增）
- **目录**：`src/server/services/worldbook/processors/`
- **文件列表**：
  - `recursion-processor.ts` - 递归处理器
  - `min-activation-processor.ts` - 最小激活数处理器
  - `context-builder.ts` - 上下文构建器
  - `index.ts` - 处理器统一导出

#### 统一引擎集合（保持）
- **文件**：`src/server/services/worldbook/engines.ts`
- **功能**：统一引擎集合，对应链路图的统一引擎设计
- **核心类**：
  - `KeywordMatchingEngine` - 关键词匹配引擎
  - `UnifiedFilterEngine` - 统一过滤引擎
  - `ProbabilityEngine` - 概率检查引擎
  - `BudgetControlEngine` - 预算控制引擎
  - `TimedEffectsEngine` - 时间效果管理引擎

#### 增强预设处理服务（保持）
- **文件**：`src/server/services/preset/enhanced-processor.ts`
- **功能**：增强预设处理服务，集成世界书内容注入
- **核心方法**：
  - `processEnhancedPresets()` - 主处理方法
  - `injectWorldbookContent()` - 世界书内容注入
  - `processOriginalPresets()` - 原有预设处理

#### 类型定义（新增）
- **文件**：`src/server/services/worldbook/types.ts`
- **功能**：世界书激活相关的类型定义
- **核心接口**：激活器接口、处理器接口、上下文类型等

### 修改文件

#### 前端消息服务
- **文件**：`src/services/chat.ts`
- **修改方法**：`ChatService.createAssistantMessage`
- **新增方法**：`processMessagesEnhanced()` - 后端增强处理调用
- **修改逻辑**：
  - 原有：`oaiMessages = this.processMessagesPreset()`
  - 修改为：`oaiMessages = await this.processMessagesEnhanced()`
  - 错误处理：失败时自动回退到原有处理

#### 数据库Schema扩展
- **文件**：`src/database/schemas/topic.ts`
- **新增字段**：
  - `messageCount: integer('message_count').default(0)` - 消息计数
  - `worldbook: jsonb('worldbook').default({})` - 运行时数据

#### 类型定义扩展
- **文件**：`src/types/worldbook.ts`
- **新增接口**：
  - `WorldbookRuntimeData` - 运行时数据结构
  - `ActivationContext` - 激活上下文
  - `WorldbookActivationResult` - 激活结果

## 🔄 执行链路

### 主要调用流程
```
用户发送消息
    ↓
ChatService.createAssistantMessage()
    ↓
processMessagesEnhanced() ← 新的集成点
    ↓
POST /api/chat/enhanced-messages
    ↓
ContextBuilder.buildActivationContext()
    ↓
WorldbookActivationService.activate()
    ↓
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
返回增强消息
    ↓
发送给AI模型
```

### 激活服务内部流程
```
WorldbookActivationService.activate()
    ↓
ContextBuilder.buildActivationContext()
    ├── 获取消息计数
    ├── 获取运行时数据
    └── 构建扫描文本
    ↓
并行执行激活模式
    ├── ConstantActivator.activate()
    ├── KeywordActivator.activate()
    ├── VectorActivator.activate()
    ├── StickyActivator.activate()
    └── DecoratorActivator.activate()
    ↓
UnifiedFilterEngine.filter()
    ├── 基础过滤
    ├── 时间效果过滤
    ├── 包含组过滤
    └── 优先级排序
    ↓
BudgetControlEngine.control()
    ↓
RecursionProcessor.process() (如果启用)
    ↓
MinActivationProcessor.process() (如果启用)
    ↓
ProbabilityEngine.check()
    ↓
TimedEffectsEngine.update()
    ↓
返回激活条目列表
```

### 预设处理集成流程
```
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
injectWorldbookContent()
    ├── 按位置分组世界书条目
    ├── 注入到系统消息位置
    ├── 注入到指定深度
    └── 处理特殊位置注入
    ↓
processOriginalPresets()
    ├── 移植现有预设处理逻辑
    ├── 变量替换处理
    └── 消息合并处理
    ↓
返回增强消息数组
```

## 🔍 核心细节说明

### 五种激活模式

#### 1. Constant模式
- **处理器**：`ConstantActivator`
- **激活逻辑**：检查条目的constant标记，始终激活标记为constant的条目
- **核心功能**：
  - 查询constant=true的条目
  - 跳过关键词匹配和概率检查
  - 直接加入激活列表

#### 2. Keyword模式
- **处理器**：`KeywordActivator`
- **激活逻辑**：基于关键词匹配激活条目
- **核心功能**：
  - 正则表达式解析和匹配
  - 字符串转换处理（大小写、特殊字符）
  - 全词匹配边界检查
  - 变量替换（{{char}}、{{user}}、{{time}}、{{date}}）
  - 选择性逻辑处理（AND_ANY、AND_ALL、NOT_ANY、NOT_ALL）

#### 3. Vector模式
- **处理器**：`VectorActivator`
- **激活逻辑**：基于向量相似度激活条目
- **核心功能**：
  - 集成现有RAG向量搜索能力
  - 语义相似度计算和阈值判断
  - 向量匹配算法优化
  - 相似度排序和筛选

#### 4. Sticky模式
- **处理器**：`StickyActivator`
- **激活逻辑**：处理粘性激活条目
- **核心功能**：
  - 检查条目的sticky状态和剩余轮数
  - 粘性状态的持久化和恢复
  - 粘性条目的优先级处理
  - 跳过概率检查和部分过滤

#### 5. Decorator模式
- **处理器**：`DecoratorActivator`
- **激活逻辑**：基于装饰器标记激活条目
- **核心功能**：
  - 装饰器解析逻辑（@@activate、@@disable等）
  - 特殊标记的识别和处理
  - 装饰器参数的解析和应用
  - 条件装饰器的动态评估

### 统一过滤引擎四层机制
1. **基础过滤**：enabled状态、用户权限、角色过滤
2. **时间效果过滤**：检查cooldown、delay、sticky状态
3. **包含组过滤**：组权重计算、组覆盖处理、组评分机制
4. **优先级排序**：按order字段排序

### 递归处理机制
- **处理器**：`RecursionProcessor`
- **核心功能**：
  - 递归延迟级别管理（availableRecursionDelayLevels）
  - 防止递归传播逻辑（preventRecursion检查）
  - 递归深度的动态调整和限制
  - 递归扫描文本的构建和更新
  - 循环处理直到无新激活或达到深度限制

### 最小激活数机制
- **处理器**：`MinActivationProcessor`
- **核心功能**：
  - 激活数量的动态检查和补充
  - 扫描深度的渐进式扩展
  - 仅对keyword模式进行扩展扫描
  - 循环处理直到满足要求或达到深度限制

### 时间效果管理
- **引擎**：`TimedEffectsEngine`
- **核心功能**：
  - **Sticky效果**：精确的轮数计算和状态管理
  - **Cooldown效果**：冷却时间的计算和检查
  - **Delay效果**：延迟激活的时间控制
  - 时间效果的优先级处理和状态持久化

### 关键词匹配算法
- **引擎**：`KeywordMatchingEngine`
- **核心功能**：
  - 正则表达式的解析和异常处理
  - 字符串转换的完整实现
  - 全词匹配的边界处理算法
  - 变量替换的递归处理和嵌套支持
  - 选择性逻辑的短路优化

### 预算控制机制
- **引擎**：`BudgetControlEngine`
- **核心功能**：
  - Token估算：中文字符/1.5 + 英文字符/4
  - 按优先级排序条目，逐个累计token
  - 超出预算时停止添加，为溢出条目设置cooldown

### 包含组功能
- **引擎**：`UnifiedFilterEngine`
- **核心功能**：
  - 组评分的具体算法实现
  - 组覆盖策略的完整逻辑
  - 组互斥处理和冲突解决
  - 组权重的动态计算和调整

## 🚀 开发实施计划

### 第一阶段：架构创建（2天）
1. **创建模块化文件结构**：按设计的文件架构创建目录和文件
2. **创建激活服务**：实现主控制器和各个激活模块
3. **实现激活器接口**：定义统一的激活器接口和基础实现

### 第二阶段：功能实现（2天）
1. **实现激活模式**：实现每个激活模式的具体逻辑
2. **实现处理器**：实现递归处理器和最小激活数处理器
3. **实现引擎功能**：实现关键词匹配、时间效果、包含组功能

### 第三阶段：集成测试（1天）
1. **模块集成**：确保各个模块之间的协调工作
2. **功能测试**：验证每个激活模式和处理器的正确性
3. **性能优化**：优化模块间的调用和数据传递

### 预期效果
- ✅ 模块化架构，职责清晰，易于维护
- ✅ 支持五种世界书激活模式
- ✅ 支持递归处理和最小激活数机制
- ✅ 支持时间效果和包含组功能
- ✅ 与现有预设系统无缝集成
- ✅ 错误处理完善，稳定性高

**总开发周期：5天，架构清晰，功能完整，满足项目需求。**
