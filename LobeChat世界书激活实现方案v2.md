# LobeChat 世界书激活实现方案 v2 - 重新设计版

## 📝 文档修正说明

**版本**：v2.1（基于代码分析修正版）
**修正内容**：
- 修正了数据库schema描述，确认现有worldbook.ts字段已基本完整
- 明确了topics表需要添加的字段
- 统一了字段命名规范
- 确认了文件路径的准确性

## 🎯 核心目标

在LobeChat现有架构中集成世界书激活功能，通过后端统一处理实现消息预处理时的智能内容注入。

## 🔍 基于链路图v8和现有代码的重新分析

**代码分析说明**：本方案基于对SillyTavern和LobeChat两个项目的深度代码分析，确保设计的准确性和可行性。

### 现有processMessagesPreset分析

通过分析`src/services/chat.ts`中的processMessagesPreset方法（837-973行），发现其核心逻辑：

1. **简单的预设处理**：
   - 获取agentConfig和preset配置
   - 过滤启用的prompts并排序
   - 处理注入和非注入两类prompts
   - 变量替换和消息合并

2. **实际功能有限**：
   - 主要是变量替换（{{char}}、{{user}}等）
   - 简单的消息注入到历史记录中
   - 预设提示词的组装和合并

3. **不需要过度拆分**：
   - 整个方法只有136行，逻辑相对简单
   - 主要是数据处理和变量替换，不涉及复杂算法

### 基于链路图v8的合理架构设计

根据链路图v8修正版的分析，世界书激活是一个复杂的多阶段流程，需要合理的模块划分：

#### 核心设计原则

1. **按功能模块划分**：根据链路图的主要功能块进行模块划分
2. **统一引擎设计**：关键词匹配引擎、统一过滤引擎等作为可复用组件
3. **避免过度拆分**：相关性强的功能保持在同一模块中
4. **保持逻辑内聚**：每个模块负责完整的功能闭环

## 📁 基于链路图v8的合理文件架构

### 1. 后端核心文件（3个主要文件）

#### `src/app/(backend)/webapi/chat/enhanced-messages/route.ts`
- **新增**：后端统一消息处理接口
- **功能**：接收messages和topicId，调用世界书激活和预设处理，返回增强消息

#### `src/server/services/worldbook/activation-service.ts`
- **新增**：世界书激活核心服务（对应链路图的主激活流程）
- **核心方法**：
  - `activate(context): Promise<WorldbookActivationResult>` - 主激活方法（链路图入口）
  - `buildActivationContext(topicId, userId): Promise<ActivationContext>` - 构建激活上下文
  - `executeActivationModes(context): Promise<WorldbookEntry[]>` - 并行执行五种激活模式
  - `processRecursionLoop(entries, context): Promise<WorldbookEntry[]>` - 递归处理循环
  - `processMinActivationLoop(entries, context): Promise<WorldbookEntry[]>` - 最小激活数循环
  - `finalizeActivation(entries, context): Promise<WorldbookEntry[]>` - 最终激活处理

#### `src/server/services/worldbook/engines.ts`
- **新增**：统一引擎集合（对应链路图的统一引擎设计）
- **核心类**：
  - `KeywordMatchingEngine` - 关键词匹配引擎（支持主激活、递归、最小激活数三种模式）
  - `UnifiedFilterEngine` - 统一过滤引擎（四层过滤机制）
  - `ProbabilityEngine` - 概率检查引擎
  - `BudgetControlEngine` - 预算控制引擎
  - `TimedEffectsEngine` - 时间效果管理引擎

#### `src/server/services/preset/enhanced-processor.ts`
- **新增**：增强预设处理服务（简化版，不过度拆分）
- **核心方法**：
  - `processEnhancedPresets(messages, worldbookEntries, context): Promise<OpenAIChatMessage[]>` - 主处理方法
  - `injectWorldbookContent(messages, entries): OpenAIChatMessage[]` - 世界书内容注入
  - `processOriginalPresets(messages, agentConfig, nickname): OpenAIChatMessage[]` - 原有预设处理（移植）

### 2. 前端修改文件（最小化修改）

#### `src/services/chat.ts`
- **修改**：ChatService.createAssistantMessage方法
- **新增方法**：
  - `processMessagesEnhanced(messages, topicId): Promise<OpenAIChatMessage[]>` - 后端增强处理调用
- **修改逻辑**：
  - 原有：`oaiMessages = this.processMessagesPreset({ messages: oaiMessages, model: payload.model })`
  - 修改为：`oaiMessages = await this.processMessagesEnhanced(oaiMessages, options?.topicId, payload.model)`
  - 错误处理：失败时自动回退到原有processMessagesPreset处理

### 3. 数据库Schema扩展（基于链路图v8的数据存储设计）

**说明**：经过代码分析，worldbook相关的数据库字段已基本完整，主要需要扩展topics表。

#### `src/database/schemas/topic.ts`
- **修改**：扩展topics表定义（对应链路图的数据库修正设计）
- **需要新增字段**：
  - `messageCount: integer('message_count').default(0)` - 消息计数（链路图：GET_MESSAGE_COUNT）
  - `worldbook: jsonb('worldbook').$type<WorldbookRuntimeData>().default({})` - 运行时数据（链路图：SAVE_RUNTIME_DATA）
- **当前状态**：topics表中尚未包含这两个字段，需要在实施时添加

#### `src/database/schemas/worldbook.ts`
- **确认**：worldbook_chunks表字段已基本完整
- **现有字段检查**：
  - `activationMode` - 激活模式字段（已存在）
  - `position` - 注入位置字段（已存在）
  - `order` - 优先级排序字段（已存在）
  - `sticky` - 粘性持续轮数（已存在，使用简化命名）
  - `cooldown` - 冷却持续轮数（已存在，使用简化命名）
  - `delay` - 延迟轮数（已存在，使用简化命名）
  - `probability` - 激活概率（已存在）
  - `useProbability` - 是否启用概率（已存在）
  - `excludeRecursion` - 排除递归（已存在）
  - `delayUntilRecursion` - 递归延迟级别（已存在）
  - `groupName` - 包含组名称（已存在，字段名为groupName）
  - `groupWeight` - 组权重（已存在）
  - `decorators` - 装饰器支持（已存在，使用数组格式）

### 4. 类型定义扩展

#### `src/types/worldbook.ts`
- **新增接口**：
  - `WorldbookRuntimeData` - 运行时数据结构
  - `ActivationContext` - 激活上下文
  - `WorldbookActivationResult` - 激活结果
- **现有接口**：
  - `WorldbookEntry` - 世界书条目接口（已存在，字段基本完整）

## 🔧 基于链路图v8的核心实现逻辑

### 后端统一处理流程

#### API接口设计
- **路由**：`POST /api/chat/enhanced-messages`
- **请求参数**：`{ messages: OpenAIChatMessage[], topicId: string, model: string }`
- **响应结果**：`{ enhancedMessages: OpenAIChatMessage[], metadata?: {...} }`
- **处理流程**：
  1. **身份验证**：`const { userId } = await auth()` - 获取用户身份
  2. **构建上下文**：`buildActivationContext(topicId, userId)` - 对应链路图INIT_CONTEXT
  3. **激活世界书**：`WorldbookActivationService.activate(context)` - 对应链路图主激活流程
  4. **处理预设**：`EnhancedPresetProcessor.processEnhancedPresets(messages, entries, context, model)` - 集成世界书和原有预设
  5. **返回结果**：包含增强消息和处理元数据

### 合理的架构设计分析

#### 基于链路图v8的模块划分原则

1. **activation-service.ts - 主激活流程控制器**：
   - 对应链路图的主激活流程（START_MAIN_ACTIVATION到FINAL_RESULT）
   - 负责流程控制，不包含具体算法实现
   - 调用各个引擎完成具体功能

2. **engines.ts - 统一引擎集合**：
   - 对应链路图中的"统一引擎"设计理念
   - 关键词匹配引擎：支持主激活、递归、最小激活数三种模式
   - 统一过滤引擎：四层过滤机制的完整实现
   - 其他专用引擎：概率、预算、时间效果等

3. **enhanced-processor.ts - 简化的预设处理**：
   - 不过度拆分processMessagesPreset的逻辑
   - 主要功能：世界书内容注入 + 原有预设处理
   - 保持与现有代码的兼容性

#### 为什么这样设计？

1. **遵循链路图的统一引擎设计**：
   - 链路图明确标注了"统一引擎"的概念
   - 关键词匹配引擎在三个地方复用（主激活、递归、最小激活数）
   - 统一过滤引擎在三个地方复用（主激活、递归、最小激活数）

2. **避免过度拆分processMessagesPreset**：
   - 现有代码只有136行，功能相对简单
   - 主要是变量替换和消息组装，不需要复杂的架构
   - 世界书注入是在现有逻辑基础上的扩展

3. **保持合理的复杂度**：
   - 3个核心文件，职责清晰
   - 每个文件负责完整的功能模块
   - 便于理解、测试和维护

### WorldbookActivationService详细设计

#### 主激活流程控制（对应链路图主流程）

```typescript
// src/server/services/worldbook/activation-service.ts
export class WorldbookActivationService {

  async activate(context: ActivationContext): Promise<WorldbookActivationResult> {
    // 1. 初始化和清理（对应链路图INIT_CONTEXT到START_MAIN_ACTIVATION）
    await this.initializeContext(context);
    await this.cleanupExpiredEffects(context);

    // 2. 主激活流程（对应链路图START_MAIN_ACTIVATION）
    let activatedEntries = await this.executeMainActivation(context);

    // 3. 统一过滤（对应链路图FILTER_ENGINE）
    activatedEntries = await this.engines.unifiedFilter.filter(activatedEntries, context);

    // 4. 预算控制（对应链路图BUDGET_CONTROL）
    activatedEntries = await this.engines.budgetControl.control(activatedEntries, context);

    // 5. 递归处理循环（对应链路图RECURSION_CHECK到POST_RECURSION_PROCESSING）
    activatedEntries = await this.processRecursionLoop(activatedEntries, context);

    // 6. 最小激活数循环（对应链路图CHECK_MIN_ACTIVATIONS_ENABLED到PROBABILITY_ENGINE_FINAL）
    activatedEntries = await this.processMinActivationLoop(activatedEntries, context);

    // 7. 最终概率检查（对应链路图PROBABILITY_ENGINE_FINAL）
    activatedEntries = await this.engines.probability.check(activatedEntries, context);

    // 8. 更新运行时数据（对应链路图SET_TIMED_EFFECTS到SAVE_RUNTIME_DATA）
    await this.engines.timedEffects.update(activatedEntries, context);

    return { activatedEntries, metadata: this.buildMetadata(context) };
  }

  private async executeMainActivation(context: ActivationContext): Promise<WorldbookEntry[]> {
    // 并行执行五种激活模式（对应链路图的五个并行分支）
    const results = await Promise.all([
      this.activateConstant(context),
      this.activateDecorator(context),
      this.engines.keywordMatching.activate(context, 'main'), // 使用统一引擎
      this.activateVector(context),
      this.activateSticky(context)
    ]);

    // 合并结果并去重（对应链路图COLLECT_ALL）
    return this.mergeAndDeduplicateEntries(results.flat());
  }

  private async processRecursionLoop(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    // 对应链路图的递归处理循环
    if (!context.config.recursiveEnabled) return entries;

    let allEntries = [...entries];
    let recursionDepth = 0;

    while (recursionDepth < context.config.maxRecursionDepth) {
      // 使用统一的关键词匹配引擎进行递归
      const recursiveEntries = await this.engines.keywordMatching.activate(
        { ...context, scanText: this.buildRecursiveScanText(allEntries, context) },
        'recursion'
      );

      if (recursiveEntries.length === 0) break;

      // 应用递归条件过滤
      const validEntries = this.filterRecursionConditions(recursiveEntries, context);
      if (validEntries.length === 0) break;

      // 统一过滤引擎处理
      const filteredEntries = await this.engines.unifiedFilter.filter(validEntries, context);

      allEntries.push(...filteredEntries);
      recursionDepth++;
    }

    return allEntries;
  }

  private async processMinActivationLoop(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    // 对应链路图的最小激活数处理循环
    if (!context.config.minActivationsEnabled || entries.length >= context.config.minActivations) {
      return entries;
    }

    let allEntries = [...entries];
    let scanDepth = 10;

    while (allEntries.length < context.config.minActivations && scanDepth <= context.config.maxScanDepth) {
      // 使用统一的关键词匹配引擎进行扩展扫描
      const extendedEntries = await this.engines.keywordMatching.activate(
        { ...context, scanText: this.buildExtendedScanText(context, scanDepth) },
        'minActivation'
      );

      // 过滤已存在的条目
      const newEntries = extendedEntries.filter(entry =>
        !allEntries.some(existing => existing.id === entry.id)
      );

      if (newEntries.length === 0) break;

      // 统一过滤引擎处理
      const filteredEntries = await this.engines.unifiedFilter.filter(newEntries, context);

      allEntries.push(...filteredEntries);
      scanDepth += 10;
    }

    return allEntries;
  }
}
```

#### 统一引擎设计（对应链路图的统一引擎）

```typescript
// src/server/services/worldbook/engines.ts
export class UnifiedEngines {

  keywordMatching = new KeywordMatchingEngine();
  unifiedFilter = new UnifiedFilterEngine();
  probability = new ProbabilityEngine();
  budgetControl = new BudgetControlEngine();
  timedEffects = new TimedEffectsEngine();

}

// 关键词匹配引擎（支持三种模式）
export class KeywordMatchingEngine {

  async activate(context: ActivationContext, mode: 'main' | 'recursion' | 'minActivation'): Promise<WorldbookEntry[]> {
    // 根据模式调整扫描文本和过滤条件
    const scanText = this.buildScanText(context, mode);
    const entries = await this.queryKeywordEntries(context);

    const activatedEntries: WorldbookEntry[] = [];

    for (const entry of entries) {
      if (await this.matchKeywords(entry, scanText, context)) {
        activatedEntries.push(entry);
      }
    }

    return activatedEntries;
  }

  private async matchKeywords(entry: WorldbookEntry, scanText: string, context: ActivationContext): Promise<boolean> {
    // 变量替换
    const processedKeys = this.processKeywordVariables(entry.keys, context);

    // 关键词匹配
    const matches = processedKeys.map(keyword => this.executeKeywordMatch(keyword, scanText, entry.matchingOptions));

    // 选择性逻辑
    return this.applySelectiveLogic(entry.selectiveLogic, matches);
  }
}

// 统一过滤引擎（四层过滤）
export class UnifiedFilterEngine {

  async filter(entries: WorldbookEntry[], context: ActivationContext): Promise<WorldbookEntry[]> {
    let filtered = entries;

    // 四层过滤机制
    filtered = this.basicFilter(filtered, context);
    filtered = this.timedEffectsFilter(filtered, context);
    filtered = this.inclusionGroupFilter(filtered, context);
    filtered = this.prioritySort(filtered, context);

    return filtered;
  }
}
```

### EnhancedPresetProcessor设计（简化版）

#### 核心设计理念

基于对现有processMessagesPreset的分析，该方法主要功能是：
1. 变量替换（{{char}}、{{user}}等）
2. 预设提示词的组装和注入
3. 历史消息的处理和合并

因此，增强版本应该：
1. **保持原有逻辑不变**：确保兼容性
2. **添加世界书注入**：在适当位置注入世界书内容
3. **最小化修改**：不过度重构现有代码

#### 具体实现设计

```typescript
// src/server/services/preset/enhanced-processor.ts
export class EnhancedPresetProcessor {

  async processEnhancedPresets(
    messages: OpenAIChatMessage[],
    worldbookEntries: WorldbookEntry[],
    context: ProcessingContext,
    model: string
  ): Promise<OpenAIChatMessage[]> {

    // 1. 先注入世界书内容到相应位置
    const messagesWithWorldbook = this.injectWorldbookContent(messages, worldbookEntries);

    // 2. 调用原有的预设处理逻辑（移植自前端）
    const processedMessages = this.processOriginalPresets(messagesWithWorldbook, context.agentConfig, context.userProfile.nickname, model);

    return processedMessages;
  }

  private injectWorldbookContent(messages: OpenAIChatMessage[], entries: WorldbookEntry[]): OpenAIChatMessage[] {
    // 按位置分组世界书条目
    const grouped = this.groupEntriesByPosition(entries);

    let enhanced = [...messages];

    // 注入到系统消息位置
    enhanced = this.injectToSystemPosition(enhanced, grouped.before, grouped.after);

    // 注入到指定深度
    enhanced = this.injectAtDepth(enhanced, grouped.atDepth);

    // 其他位置的注入将在预设处理中通过特殊标识符处理
    // 这样可以复用现有的预设系统，而不需要重新实现

    return enhanced;
  }

  private processOriginalPresets(
    messages: OpenAIChatMessage[],
    agentConfig: LobeAgentConfig,
    nickname: string,
    model: string
  ): OpenAIChatMessage[] {
    // 这里移植现有的processMessagesPreset逻辑（837-973行）
    // 保持原有逻辑不变，确保兼容性

    // 未选择预设的情况
    if (!agentConfig.preset) {
      let messagesCopy = structuredClone(messages);
      if (messagesCopy[0]?.role === 'system' && typeof messagesCopy[0].content === 'string') {
        messagesCopy[0].content = processBaseVariables(messagesCopy[0].content, agentConfig.title, nickname);
      }
      return messagesCopy;
    }

    // 获取预设配置
    const preset = this.getPresetConfig(agentConfig.preset);
    if (!preset || (!preset.supportModel.includes(model) && !preset.supportModel.includes('all'))) {
      // 预设不支持当前模型，使用基础处理
      let messagesCopy = structuredClone(messages);
      if (messagesCopy[0]?.role === 'system' && typeof messagesCopy[0].content === 'string') {
        messagesCopy[0].content = processBaseVariables(messagesCopy[0].content, agentConfig.title, nickname);
      }
      return messagesCopy;
    }

    // 处理预设提示词（复用现有逻辑）
    return this.processPresetPrompts(messages, preset, agentConfig, nickname);
  }

  private groupEntriesByPosition(entries: WorldbookEntry[]): GroupedEntries {
    return {
      before: entries.filter(e => e.position === 'before'),
      after: entries.filter(e => e.position === 'after'),
      atDepth: entries.filter(e => e.position === 'at_depth'),
      anTop: entries.filter(e => e.position === 'an_top'),
      anBottom: entries.filter(e => e.position === 'an_bottom'),
      emTop: entries.filter(e => e.position === 'em_top'),
      emBottom: entries.filter(e => e.position === 'em_bottom')
    };
  }
}
```

#### 世界书与预设系统的集成策略

1. **通过预设标识符集成**：
   - 在预设系统中添加特殊标识符：`worldInfoBefore`、`worldInfoAfter`、`authorNoteTop`等
   - 在处理预设时，将对应位置的世界书内容注入到这些标识符位置
   - 保持与现有预设系统的完全兼容

2. **最小化代码修改**：
   - 不重构现有的processMessagesPreset逻辑
   - 只在必要位置添加世界书内容注入
   - 保持原有的变量替换和消息合并逻辑

3. **渐进式集成**：
   - 第一阶段：实现基础的BEFORE、AFTER、AT_DEPTH注入
   - 第二阶段：通过预设系统集成AN_TOP、AN_BOTTOM等高级位置
   - 第三阶段：添加插入策略和高级功能

### 前端调用替换详细设计

#### 修改ChatService的具体实现
1. **processMessagesEnhanced方法**：
   ```typescript
   private processMessagesEnhanced = async (
     messages: OpenAIChatMessage[],
     topicId?: string
   ): Promise<OpenAIChatMessage[]> => {

     if (!topicId) {
       // 如果没有topicId，回退到基础处理
       return this.processMessagesPreset({ messages, model: 'default' });
     }

     try {
       const response = await fetch('/api/chat/enhanced-messages', {
         method: 'POST',
         headers: { 'Content-Type': 'application/json' },
         body: JSON.stringify({ messages, topicId }),
       });

       if (!response.ok) {
         throw new Error(`Enhanced processing failed: ${response.statusText}`);
       }

       const { enhancedMessages } = await response.json();
       return enhancedMessages;

     } catch (error) {
       console.error('Enhanced message processing failed, falling back to basic processing:', error);
       // 出错时回退到原有处理
       return this.processMessagesPreset({ messages, model: 'default' });
     }
   };
   ```

2. **createAssistantMessage方法修改**：
   - **原有代码**：`oaiMessages = this.processMessagesPreset({ messages: oaiMessages, model: payload.model });`
   - **新的代码**：`oaiMessages = await this.processMessagesEnhanced(oaiMessages, options?.topicId);`
   - **关键变化**：从同步调用改为异步调用，需要添加await关键字

3. **错误处理策略**：
   - **网络错误**：fetch失败时自动回退
   - **服务器错误**：response.ok=false时自动回退
   - **解析错误**：JSON解析失败时自动回退
   - **参数错误**：topicId为空时直接使用原有处理

4. **兼容性保证**：
   - 保持原有processMessagesPreset方法不变
   - 新旧处理方式可以并存
   - 通过配置开关控制是否启用新处理方式

## 🔄 调用链路设计

### 主要调用流程

```
用户发送消息
    ↓
ChatService.createAssistantMessage()
    ↓
processMessages()
    ↓
processMessagesEnhanced() ← 新的集成点
    ↓
POST /api/chat/enhanced-messages
    ↓
buildProcessingContext()
    ↓
WorldbookActivationService.activate()
    ↓
PresetProcessor.processPresets()
    ↓
返回增强消息
    ↓
发送给AI模型
```

### 激活服务内部流程

```
WorldbookActivationService.activate()
    ↓
buildActivationContext()
    ├── TopicModel.getMessageCount()
    ├── TopicModel.getWorldbookRuntimeData()
    └── 构建扫描文本
    ↓
executeActivationModes() (并行执行)
    ├── activateConstant()
    ├── activateKeyword()
    ├── activateVector()
    ├── activateSticky()
    └── activateDecorator()
    ↓
UnifiedFilterEngine.filterEntries()
    ├── 基础过滤
    ├── 时间效果过滤
    ├── 包含组过滤
    └── 优先级排序
    ↓
TokenEstimator.controlBudget()
    ↓
RecursionProcessor.processRecursion() (如果启用)
    ↓
MinActivationsProcessor.processMinActivations() (如果启用)
    ↓
ProbabilityChecker.checkProbability()
    ↓
返回激活条目列表
    ↓
PresetProcessor.processPresets() (注入世界书内容)
    ├── injectToSystemMessage()
    ├── injectToAuthorNote()
    ├── injectToExampleDialogue()
    └── injectAtDepth()
    ↓
TimedEffectsManager.updateTimedEffects()
    ├── TopicModel.saveWorldbookRuntimeData()
    └── TopicModel.incrementMessageCount()
```

## 🔍 关键技术细节

### 关键词匹配算法的具体实现
1. **三种匹配模式**：
   - **字面匹配**：`scanText.includes(keyword)` 或 `scanText.toLowerCase().includes(keyword.toLowerCase())`
   - **全词匹配**：`new RegExp(\`\\b${escapeRegex(keyword)}\\b\`, caseSensitive ? 'g' : 'gi')`
   - **正则匹配**：`new RegExp(keyword, caseSensitive ? 'g' : 'gi')`

2. **选择性逻辑的组合算法**：
   - **AND_ANY**：`matches.some(m => m)` - 任意一个关键词匹配即可
   - **AND_ALL**：`matches.every(m => m)` - 所有关键词都必须匹配
   - **NOT_ANY**：`!matches.some(m => m)` - 任意一个关键词都不能匹配
   - **NOT_ALL**：`!matches.every(m => m)` - 不是所有关键词都匹配
   - **组合逻辑**：主次关键词通过AND、OR、NOT进行组合

3. **变量替换的具体实现**：
   - **{{char}}** → `context.agentConfig.title || 'Assistant'`
   - **{{user}}** → `context.userProfile.nickname || 'User'`
   - **{{time}}** → `new Date().toLocaleTimeString('zh-CN', { hour12: false })`
   - **{{date}}** → `new Date().toLocaleDateString('zh-CN')`

4. **异常处理机制**：
   - 正则表达式语法错误时自动回退到字面匹配
   - 记录警告日志但不中断处理流程

### 时间效果管理的具体机制
1. **数据结构**：
   ```typescript
   interface TimedEffect {
     sticky: number;    // 粘性效果剩余轮数
     cooldown: number;  // 冷却效果剩余轮数
     delay: number;     // 延迟效果剩余轮数
   }
   ```

2. **更新逻辑**：
   - **每轮开始**：所有计数器递减1，为0时删除记录
   - **激活时设置**：根据条目配置设置相应的计数器
   - **并发安全**：使用数据库事务确保数据一致性

3. **效果优先级**：
   - **sticky优先**：sticky条目跳过概率检查和部分过滤
   - **cooldown阻断**：cooldown > 0的条目完全不能激活
   - **delay延迟**：delay > 0的条目暂时不能激活

### 扫描文本构建的具体算法
1. **基础扫描文本**：
   ```typescript
   const recentMessages = messages.slice(-10);
   const scanTexts = recentMessages.map(msg => msg.content).filter(Boolean);
   if (context.agentConfig.title) scanTexts.unshift(context.agentConfig.title);
   if (context.userProfile.nickname) scanTexts.unshift(context.userProfile.nickname);
   return scanTexts.join('\n');
   ```

2. **递归扫描文本**：
   ```typescript
   const activatedContent = activatedEntries.map(entry => entry.content).join('\n');
   return basicScanText + '\n' + activatedContent;
   ```

3. **扩展扫描文本**：
   ```typescript
   const extendedMessages = messages.slice(-(10 + extendedDepth));
   return extendedMessages.map(msg => msg.content).filter(Boolean).join('\n');
   ```

### Token估算和预算控制的具体算法
1. **Token估算公式**：
   ```typescript
   const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
   const otherChars = text.length - chineseChars;
   return Math.ceil(chineseChars / 1.5 + otherChars / 4);
   ```

2. **预算控制流程**：
   - 按优先级排序条目：`entries.sort((a, b) => (a.order || 100) - (b.order || 100))`
   - 逐个累计token：`totalTokens += estimateTokens(entry.content)`
   - 超出预算时停止：`if (totalTokens + entryTokens > maxTokens) break`
   - 为溢出条目设置cooldown：`runtimeData.timedEffects[entry.id].cooldown = 2`

## 🚀 实施计划

### 第一阶段：后端开发（3天）

1. **创建后端接口**
   - 实现 `/api/chat/enhanced-messages` 路由
   - 添加请求参数验证和错误处理

2. **开发世界书激活服务**
   - 实现五种激活模式
   - 实现四层过滤机制
   - 实现递归和最小激活数处理

3. **开发预设处理服务**
   - 移植前端processMessagesPreset逻辑
   - 实现世界书内容注入逻辑
   - 实现七种注入位置支持

### 第二阶段：前端替换（1天）

1. **修改ChatService**
   - 注释掉原有processMessagesPreset调用
   - 实现processMessagesEnhanced方法
   - 添加错误处理和回退机制

2. **测试验证**
   - 功能测试：确保世界书激活正常
   - 错误测试：验证回退机制
   - 性能测试：对比处理性能

### 第三阶段：优化完善（1天）

1. **性能优化**
   - 添加缓存机制
   - 优化数据库查询
   - 并行处理优化

2. **监控和日志**
   - 添加处理时间监控
   - 添加激活统计日志
   - 添加错误追踪

## ✅ 预期效果

### 功能完整性
- ✅ 支持五种世界书激活模式
- ✅ 支持七种内容注入位置
- ✅ 支持完整的过滤和处理机制
- ✅ 与现有预设系统无缝集成

### 架构优势
- ✅ 后端统一处理，逻辑集中
- ✅ 简化的API设计，易于维护
- ✅ 职责分离清晰，扩展性好
- ✅ 错误处理完善，稳定性高

### 开发效率
- ✅ 总开发周期：5天
- ✅ 主要工作：topics表字段添加、后端服务开发、前端集成
- ✅ 直接替换，风险可控
- ✅ 便于后续功能扩展

## 📋 实施前准备工作

### 数据库迁移
1. **topics表字段添加**：
   - 添加`message_count`字段（整型，默认0）
   - 添加`worldbook`字段（JSONB类型，默认空对象）

2. **索引优化**：
   - 为新增字段添加适当索引以提升查询性能

### 字段命名统一
- 使用现有schema中的简化命名规范（如`sticky`而非`stickyDuration`）
- 确保文档描述与实际实现保持一致

**v2方案设计完成，架构清晰，实施简单，完全满足项目需求。**
