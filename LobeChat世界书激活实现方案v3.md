# LobeChat 世界书激活实现方案 v3

## 🎯 宏观设计视角

### 核心目标
在LobeChat现有架构中集成世界书激活功能，通过后端统一处理实现消息预处理时的智能内容注入。

### 设计原则
1. **后端统一处理**：将世界书激活逻辑从前端移至后端，实现统一管理
2. **引擎化设计**：采用统一引擎架构，支持关键词匹配、过滤、概率控制等功能
3. **最小化前端修改**：仅修改消息处理调用方式，保持现有逻辑不变
4. **渐进式集成**：与现有预设系统无缝集成，支持错误回退

### 整体架构
```
前端消息处理 → 后端增强API → 世界书激活服务 → 预设处理服务 → 增强消息返回
```

## 📁 文件清单

### 新增文件

#### 后端API接口
- **文件**：`src/app/(backend)/webapi/chat/enhanced-messages/route.ts`
- **功能**：接收messages和topicId，调用世界书激活和预设处理，返回增强消息
- **核心方法**：POST处理器，身份验证，错误处理

#### 世界书激活服务
- **文件**：`src/server/services/worldbook/activation-service.ts`
- **功能**：世界书激活核心服务，对应链路图主激活流程
- **核心方法**：
  - `activate()` - 主激活方法
  - `buildActivationContext()` - 构建激活上下文
  - `executeActivationModes()` - 并行执行五种激活模式
  - `processRecursionLoop()` - 递归处理循环
  - `processMinActivationLoop()` - 最小激活数循环
  - `finalizeActivation()` - 最终激活处理

#### 统一引擎集合
- **文件**：`src/server/services/worldbook/engines.ts`
- **功能**：统一引擎集合，对应链路图的统一引擎设计
- **核心类**：
  - `KeywordMatchingEngine` - 关键词匹配引擎
  - `UnifiedFilterEngine` - 统一过滤引擎
  - `ProbabilityEngine` - 概率检查引擎
  - `BudgetControlEngine` - 预算控制引擎
  - `TimedEffectsEngine` - 时间效果管理引擎

#### 增强预设处理服务
- **文件**：`src/server/services/preset/enhanced-processor.ts`
- **功能**：增强预设处理服务，集成世界书内容注入
- **核心方法**：
  - `processEnhancedPresets()` - 主处理方法
  - `injectWorldbookContent()` - 世界书内容注入
  - `processOriginalPresets()` - 原有预设处理

### 修改文件

#### 前端消息服务
- **文件**：`src/services/chat.ts`
- **修改方法**：`ChatService.createAssistantMessage`
- **新增方法**：`processMessagesEnhanced()` - 后端增强处理调用
- **修改逻辑**：
  - 原有：`oaiMessages = this.processMessagesPreset()`
  - 修改为：`oaiMessages = await this.processMessagesEnhanced()`
  - 错误处理：失败时自动回退到原有处理

#### 数据库Schema扩展
- **文件**：`src/database/schemas/topic.ts`
- **新增字段**：
  - `messageCount: integer('message_count').default(0)` - 消息计数
  - `worldbook: jsonb('worldbook').default({})` - 运行时数据

#### 类型定义扩展
- **文件**：`src/types/worldbook.ts`
- **新增接口**：
  - `WorldbookRuntimeData` - 运行时数据结构
  - `ActivationContext` - 激活上下文
  - `WorldbookActivationResult` - 激活结果

## 🔄 执行链路

### 主要调用流程
```
用户发送消息
    ↓
ChatService.createAssistantMessage()
    ↓
processMessagesEnhanced() ← 新的集成点
    ↓
POST /api/chat/enhanced-messages
    ↓
buildActivationContext()
    ↓
WorldbookActivationService.activate()
    ↓
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
返回增强消息
    ↓
发送给AI模型
```

### 激活服务内部流程
```
WorldbookActivationService.activate()
    ↓
buildActivationContext()
    ├── 获取消息计数
    ├── 获取运行时数据
    └── 构建扫描文本
    ↓
executeActivationModes() (并行执行)
    ├── activateConstant()
    ├── activateKeyword()
    ├── activateVector()
    ├── activateSticky()
    └── activateDecorator()
    ↓
UnifiedFilterEngine.filter()
    ├── 基础过滤
    ├── 时间效果过滤
    ├── 包含组过滤
    └── 优先级排序
    ↓
BudgetControlEngine.control()
    ↓
processRecursionLoop() (如果启用)
    ↓
processMinActivationLoop() (如果启用)
    ↓
ProbabilityEngine.check()
    ↓
TimedEffectsEngine.update()
    ↓
返回激活条目列表
```

### 预设处理集成流程
```
EnhancedPresetProcessor.processEnhancedPresets()
    ↓
injectWorldbookContent()
    ├── 按位置分组世界书条目
    ├── 注入到系统消息位置
    ├── 注入到指定深度
    └── 处理特殊位置注入
    ↓
processOriginalPresets()
    ├── 移植现有预设处理逻辑
    ├── 变量替换处理
    └── 消息合并处理
    ↓
返回增强消息数组
```

## 🔍 核心细节说明

### 五种激活模式
1. **Constant模式**：始终激活的条目
2. **Keyword模式**：基于关键词匹配激活
3. **Vector模式**：基于向量相似度激活
4. **Sticky模式**：粘性激活条目
5. **Decorator模式**：装饰器触发激活

### 统一过滤引擎四层机制
1. **基础过滤**：enabled状态、用户权限、角色过滤
2. **时间效果过滤**：检查cooldown、delay、sticky状态
3. **包含组过滤**：组权重计算、组覆盖处理、组评分机制
4. **优先级排序**：按order字段排序

### 递归处理机制
- 检查递归启用状态和深度限制
- 构建递归扫描文本（原文本 + 激活内容）
- 仅对keyword模式进行递归激活
- 检查excludeRecursion和delayUntilRecursion条件
- 循环处理直到无新激活或达到深度限制

### 最小激活数机制
- 检查当前激活数是否满足最小要求
- 不满足时扩展扫描深度
- 仅对keyword模式进行扩展扫描
- 循环处理直到满足要求或达到深度限制

### 时间效果管理
- **Sticky效果**：条目在指定轮数内保持激活
- **Cooldown效果**：条目在指定轮数内无法激活
- **Delay效果**：条目延迟指定轮数后才能激活

### 关键词匹配算法
- 支持正则表达式、大小写敏感、全词匹配三种模式
- 实现AND_ANY、AND_ALL、NOT_ANY、NOT_ALL四种选择性逻辑
- 支持变量替换（{{char}}、{{user}}、{{time}}、{{date}}）
- 正则表达式异常时自动回退到字面匹配

### 预算控制机制
- Token估算：中文字符/1.5 + 英文字符/4
- 按优先级排序条目，逐个累计token
- 超出预算时停止添加，为溢出条目设置cooldown

## 🚀 开发实施计划

### 第一阶段：后端开发（3天）
1. **创建后端接口**：实现enhanced-messages路由，添加参数验证和错误处理
2. **开发激活服务**：实现五种激活模式、四层过滤机制、递归和最小激活数处理
3. **开发预设处理**：移植前端逻辑、实现世界书内容注入、支持七种注入位置

### 第二阶段：前端集成（1天）
1. **修改ChatService**：替换processMessagesPreset调用、实现processMessagesEnhanced方法
2. **添加错误处理**：实现回退机制、网络错误处理、参数验证

### 第三阶段：测试优化（1天）
1. **功能测试**：验证世界书激活正常、测试各种激活模式、验证递归和最小激活数
2. **性能优化**：添加缓存机制、优化数据库查询、并行处理优化
3. **监控日志**：添加处理时间监控、激活统计日志、错误追踪

### 预期效果
- ✅ 支持五种世界书激活模式
- ✅ 支持七种内容注入位置
- ✅ 支持完整的过滤和处理机制
- ✅ 与现有预设系统无缝集成
- ✅ 后端统一处理，逻辑集中
- ✅ 错误处理完善，稳定性高

**总开发周期：5天，架构清晰，实施简单，完全满足项目需求。**
